"use client";
import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useState, useEffect } from "react";
import { BlogCreateType, BlogCreate } from "@/schemaValidations/blog.schema"; // Import the schema
import { Loader } from "react-feather";
import { toast } from "react-toastify";
import { useRouter } from "next/navigation";
import { z } from "zod";
import { generateSlugFromName } from "@/utils/slugGenerator";
import FileUpload from "@/components/FileUpload";
import mediaApiRequest from "@/apiRequests/media";
import categoryApiRequest from "@/apiRequests/category";
import TiptapEditor from "@/components/Widget/TiptapEditor";
import envConfig from "@/config";
import { useAuth } from "@/hooks/useAuth";
import VideoUploader from "@/components/Form/UploadViddeo";

type BlogValues = z.infer<typeof BlogCreate>[0];

type AddFormProps = {
  blog?: BlogValues;
  onSubmit: (data: BlogValues) => Promise<void>; // Submit handler
};

const AddForm = ({ blog, onSubmit }: AddFormProps) => {
  const { hasPermission } = useAuth();
  const [categories, setCategories] = useState<any[]>([]);
  const [isCode, setIsCode] = useState(false);
  const [loading, setLoading] = useState(false);
  const form = useForm<BlogValues>({
    resolver: zodResolver(BlogCreate.element), // Use schema for validation
    defaultValues: blog || {
      _id: "",
      user: null,
      title: "",
      featureImg: {
        path: "",
        folder: "",
        _id: "",
      }, // Initialize featureImg as an object
      slug: "",
      desc: "",
      index: 0,
      isActive: true,
      short: "",
      categories: [],
      file: [],
      isFeature: false,
      video: null,
    },
  });

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const resCategories = await categoryApiRequest.fetchAllCategories();
        if (resCategories.payload.success) {
          setCategories(resCategories.payload.categories); // Populate the categories
        } else {
          console.error("Failed to fetch categories");
        }
      } catch (error) {
        console.error("Error fetching categories:", error);
      } finally {
        setLoading(false);
      }
    };
    fetchCategories();
  }, []);

  const { watch, setValue } = form;
  const nameValue = watch("title");
  const handleGenerateSlug = () => {
    if (!nameValue) {
      toast.error("Please enter a name to generate the slug.");
      return;
    }
    const slug = generateSlugFromName(nameValue);
    setValue("slug", slug);
  };
  const onUploadFeatureImg = async (imageFile: File): Promise<string> => {
    if (loading) return "";
    setLoading(true);
    try {
      const data = new FormData();
      data.append("imageFile", imageFile);
      const sessionToken = localStorage.getItem("sessionToken") || "";
      const result = await mediaApiRequest.postMedia(data, sessionToken);
      if (result) {
        toast.success("Đăng hình ảnh thành công.");
        setTimeout(() => {
          form.setValue("featureImg", result.payload.featureImg);
        }, 3000);
        return result.payload.featureImg.path || "";
      }
      return "";
    } catch (error: any) {
      toast.error(
        "An error occurred during update your profile. Please try again."
      );
      return "";
    } finally {
      setLoading(false);
    }
  };
  const onDeleteFeatureImg = () => {
    form.setValue("featureImg", {
      path: "",
      folder: "",
      _id: "",
    });
  };

  const content = watch("desc");

  const [uploadingFile, setUploadingFile] = useState(false);

  const onUploadFiles = async (files: FileList | File[]) => {
    setUploadingFile(true);
    try {
      const sessionToken = localStorage.getItem("sessionToken") || "";
      let uploadedFiles: { path: string }[] = form.getValues("file") || []; // Get existing files

      console.log(`Starting file upload of ${files.length} files`);

      for (const file of files) {
        console.log(`Uploading file: ${file.name}, size: ${file.size}, type: ${file.type}`);
        
        // Validate file size (340MB limit)
        const maxSize = 340 * 1024 * 1024; // 340MB
        if (file.size > maxSize) {
          toast.error(`File ${file.name} quá lớn. Kích thước tối đa là 340MB.`);
          continue;
        }

        // Validate file type - ensure this is NOT a video file
        const allowedExtensions = ['pdf', 'xlsx', 'xls', 'txt', 'doc', 'docx', 'pptx', 'rar', 'zip'];
        const videoExtensions = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv'];
        const fileExt = file.name.split('.').pop()?.toLowerCase();
        
        if (!fileExt || !allowedExtensions.includes(fileExt)) {
          toast.error(`File ${file.name} có định dạng không được hỗ trợ. Chỉ chấp nhận: ${allowedExtensions.join(', ')}`);
          continue;
        }

        // Explicitly reject video files in document upload
        if (videoExtensions.includes(fileExt)) {
          toast.error(`File ${file.name} là video. Vui lòng sử dụng phần tải video riêng.`);
          continue;
        }

        const data = new FormData();
        data.append("file", file);

        try {
          console.log(`Making file API request for: ${file.name}`);
          const result = await mediaApiRequest.postFileMedia(
            data,
            sessionToken
          );
          
          console.log(`File API response for ${file.name}:`, result);
          
          if (result && result.payload && result.payload.success && result.payload.fileUrl) {
            // Store complete file information
            const fileInfo = {
              path: result.payload.fileUrl,
              originalName: result.payload.originalName || file.name,
              size: result.payload.size || file.size,
              type: file.type
            };
            uploadedFiles.push(fileInfo);
            toast.success(`Tải file thành công: ${file.name}`);
            console.log(`Successfully uploaded file: ${file.name} -> ${result.payload.fileUrl}`);
            console.log(`Complete file info:`, fileInfo);
          } else {
            const errorMsg = result?.payload?.message || result?.payload?.error || 'Không xác định';
            toast.error(`Lỗi tải file ${file.name}: ${errorMsg}`);
          }
        } catch (err) {
          console.error(`Error uploading file ${file.name}:`, err);
          const errorMessage = err instanceof Error ? err.message : 'Không xác định';
          toast.error(`Lỗi tải file ${file.name}: ${errorMessage}`);
        }
      }

      if (uploadedFiles.length > 0) {
        console.log(`Setting uploaded files (NOT video):`, uploadedFiles);
        form.setValue("file", uploadedFiles);
        // Ensure we don't overwrite video field
        console.log(`Current video value:`, form.getValues("video"));
      }
    } catch (error) {
      console.error("Error uploading files:", error);
      toast.error("Có lỗi xảy ra khi tải tệp. Vui lòng thử lại.");
    } finally {
      setUploadingFile(false);
    }
  };

  const removeFile = (index: number) => {
    const files = form.getValues("file") || [];
    const updatedFiles = files.filter((_: any, i: number) => i !== index);
    form.setValue("file", updatedFiles);
  };

  return (
    <div>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} noValidate>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 w-full mx-auto">
            <div className="col-span-1 md:col-span-2">
              <FormField
                control={form.control}
                name="title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tiêu đề</FormLabel>
                    <FormControl>
                      <Input placeholder="Tiêu đề" type="text" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="slug"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Slug</FormLabel>
                    <FormControl>
                      <div className="flex items-center gap-2 justify-between">
                        <Input placeholder="slug" {...field} />
                        <button
                          type="button"
                          onClick={handleGenerateSlug}
                          className="btn bg-blue-500 text-white rounded"
                        >
                          Tạo slug
                        </button>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <div className="my-4">
                <FormLabel>Nội Dung</FormLabel>
                {/* Tab Switcher */}
                <div role="tablist" className="flex gap-2 my-2">
                  <span
                    role="tab"
                    className={`tab ${
                      !isCode ? "border rounded-md border-gray-500" : ""
                    }`}
                    onClick={() => setIsCode(false)}
                  >
                    Soạn Thảo Thường
                  </span>
                  <span
                    role="tab"
                    className={`tab ${
                      isCode ? "border rounded-md border-gray-500" : ""
                    }`}
                    onClick={() => setIsCode(true)}
                  >
                    Soạn HTML {isCode}
                  </span>
                </div>
                {!isCode ? (
                  <TiptapEditor
                    value={content}
                    onChange={(newContent) => setValue("desc", newContent)}
                  />
                ) : (
                  <FormField
                    control={form.control}
                    name="desc"
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <textarea
                            {...field}
                            rows={15}
                            className="w-full p-2 border border-gray-300 rounded-md"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}
              </div>
              <FormField
                control={form.control}
                name="short"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nội Dung ngắn</FormLabel>
                    <FormControl>
                      <textarea
                        {...field}
                        placeholder="Enter content here"
                        rows={5}
                        className="w-full p-2 border border-gray-300 rounded-md"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="col-span-1">
              <FormField
                control={form.control}
                name="categories"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Danh mục</FormLabel>
                    <FormControl>
                      <select
                        {...field}
                        className="w-full p-2 border border-gray-300 rounded-md"
                        multiple
                        value={field.value || []} // Make sure value is an array
                        onChange={(e) => {
                          const selectedOptions = Array.from(
                            e.target.selectedOptions,
                            (option) => option.value
                          );
                          field.onChange(selectedOptions); // Update the field with selected values
                        }}
                      >
                        <option value="">Chọn danh mục</option>
                        {categories.map((category) => (
                          <option key={category._id} value={category._id}>
                            {category.name}
                          </option>
                        ))}
                      </select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <span className="block mb-2">Ảnh đại diện</span>
              <FileUpload
                serverImageUrl={blog?.featureImg?.path}
                onUploadFeatureImg={onUploadFeatureImg}
                onDeleteFeatureImg={onDeleteFeatureImg}
              />
              <FormField
                control={form.control}
                name="isActive"
                render={({ field }) => (
                  <FormItem className="flex items-center gap-2 mt-4">
                    <FormControl>
                      <input
                        type="checkbox"
                        checked={field.value} // ✅ Ensure the checkbox state is properly controlled
                        onChange={field.onChange} // ✅ Handle changes correctly
                        className="w-5 h-5 border-gray-300 rounded"
                      />
                    </FormControl>
                    <FormLabel className="cursor-pointer">
                      Kích hoạt bài viết
                    </FormLabel>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="isFeature"
                render={({ field }) => (
                  <FormItem className="flex items-center gap-2">
                    <FormControl>
                      <input
                        type="checkbox"
                        checked={field.value ?? false}
                        onChange={(e) => field.onChange(e.target.checked)}
                        className="w-5 h-5 border-gray-300 rounded"
                      />
                    </FormControl>
                    <FormLabel className="cursor-pointer">
                      Bài viết nổi bật
                    </FormLabel>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="border p-4 rounded bg-blue-50">
                <span className="block mb-2 font-semibold text-blue-800">📎 Đăng File Tài Liệu</span>
                <p className="text-sm text-gray-600 mb-2">Chỉ cho phép: PDF, Excel, Word, PowerPoint, TXT, RAR, ZIP</p>
                <FormField
                  control={form.control}
                  name="file"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <input
                          type="file"
                          multiple
                          accept=".pdf,.xlsx,.xls,.txt,.doc,.docx,.pptx,.rar,.zip"
                          onChange={(e) => {
                            if (e.target.files?.length) {
                              onUploadFiles(Array.from(e.target.files));
                            }
                          }}
                        />
                      </FormControl>

                      {uploadingFile && <p>Uploading...</p>}

                      {field.value && field.value.length > 0 && (
                        <div className="mt-2">
                          <p>File đã được đăng:</p>
                          <ul className="list-disc">
                            {field.value
                              .filter((file: { path: string }) => file && file.path) // Filter out invalid files
                              .map((file: { path: string }, index: number) => {
                                const fileName = file.path.split("/").pop() || "(Không có tên)";
                                const fullUrl = `${envConfig.NEXT_PUBLIC_API_ENDPOINT}${file.path}`;

                                return (
                                  <li
                                    key={index}
                                    className="flex items-center gap-2 mb-1"
                                  >
                                    <a
                                      href={fullUrl}
                                      target="_blank"
                                      rel="noopener noreferrer"
                                      className="text-blue-500 truncate max-w-[200px] inline-block hover:underline"
                                      title={fileName}
                                    >
                                      {fileName}
                                    </a>
                                    {hasPermission("admin") && (
                                      <button
                                        type="button"
                                        className="text-red-500 hover:text-red-700 text-sm"
                                        onClick={() => {
                                          // Find the actual index in the original array
                                          const originalIndex = field.value.findIndex((f: { path: string }) => f && f.path === file.path);
                                          if (originalIndex !== -1) {
                                            removeFile(originalIndex);
                                          }
                                        }}
                                      >
                                        Xóa
                                      </button>
                                    )}
                                  </li>
                                );
                              })}
                          </ul>
                        </div>
                      )}

                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="mb-4 border p-4 rounded bg-green-50">
                <label className="block text-sm font-semibold mb-2 text-green-800">🎥 Tải Video Lên</label>
                <p className="text-sm text-gray-600 mb-2">Chỉ cho phép file video: MP4, AVI, MOV, WMV, etc.</p>
                <VideoUploader onUrlChange={(url) => {
                  console.log("Video URL changed:", url);
                  setValue("video", url);
                  // Don't affect file uploads
                  console.log("Current files:", form.getValues("file"));
                }} />
                {watch("video") && typeof watch("video") === "string" && watch("video").trim() !== "" && (
                  <div className="mt-4">
                    <label className="block text-sm font-medium mb-1">
                      Video URL đã tải:
                    </label>
                    <input
                      type="text"
                      className="w-full px-3 py-2 border rounded"
                      value={watch("video")}
                      readOnly
                    />
                    <div className="mt-2">
                      <label className="block text-sm font-medium mb-1">
                        Xem trước Video:
                      </label>
                      <video
                        controls
                        preload="metadata"
                        className="w-full max-w-md border rounded blog-video-preview"
                        src={watch("video")}
                        data-video-type="preview"
                      >
                        Trình duyệt không hỗ trợ video.
                      </video>
                      <button
                        type="button"
                        onClick={() => {
                          setValue("video", null);
                          console.log("Video cleared");
                        }}
                        className="mt-2 px-3 py-1 bg-red-500 text-white rounded text-sm hover:bg-red-600"
                      >
                        Xóa Video
                      </button>
                    </div>
                  </div>
                )}          
              </div>
              <FormField
                control={form.control}
                name="index"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Index</FormLabel>
                    <FormControl>
                      <input
                        className="input input-bordered w-full rounded-md"
                        type="number"
                        value={field.value} // Bind the field's value
                        onChange={(e) => field.onChange(Number(e.target.value))} // Convert value to a number
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>
          <button
            disabled={loading ? true : false}
            type="submit"
            className="btn btn-primary bg-blue-700 w-40 text-white mx-auto flex items-center mt-6"
          >
            {loading ? <Loader className="animate-spin" /> : ""}
            Submit
          </button>
        </form>
      </Form>
    </div>
  );
};

export default AddForm;
