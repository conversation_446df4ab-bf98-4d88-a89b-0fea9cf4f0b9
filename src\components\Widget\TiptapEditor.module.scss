/* TiptapEditor.module.scss */
.is-active {
    background: black;
    color: #fff;
  }
  .ProseMirror:focus {
    outline: none;
  }
  .content {
    padding: 1rem;
    min-height: 10rem;
  }
  .ProseMirror {
    > * + * {
      margin-top: 0.75em;
    }
  
    ul,
    ol {
      padding: 0 1rem;
    }
    ul {
      list-style-type: disc;
    }
    ol {
      list-style-type: decimal;
    }
    h1,
    h2,
    h3,
    h4 {
      line-height: 1.1;
      font-weight: 600;
    }
    h1 {
      font-size: 2em;
    }
    h2 {
      font-size: 1.6em;
    }
    h3 {
      font-size: 1.4em;
    }
    h4 {
      font-size: 1.2em;
    }
  
    code {
      background-color: rgba(#616161, 0.1);
      color: #616161;
    }
    a {
      color: #076db6;
      text-decoration: underline;
      text-underline-position: under;
    }
    pre {
      background: #0d0d0d;
      color: #fff;
      font-family: 'JetBrainsMono', monospace;
      padding: 0.75rem 1rem;
      border-radius: 0.5rem;
  
      code {
        color: inherit;
        padding: 0;
        background: none;
        font-size: 0.8rem;
      }
    }
  
    img {
      max-width: 100%;
      height: auto;
    }
  
    blockquote {
      padding-left: 1rem;
      border-left: 2px solid rgba(#0d0d0d, 0.1);
    }
  
    hr {
      border: none;
      border-top: 2px solid rgba(#0d0d0d, 0.1);
      margin: 2rem 0;
    }
  }
  
  .bubble-menu {
    display: flex;
    background-color: #0d0d0d;
    padding: 0.2rem;
    border-radius: 0.5rem;
  
    span {
      border: none;
      background: none;
      color: #fff;
      font-size: 0.85rem;
      font-weight: 500;
      padding: 0 0.2rem;
      opacity: 0.6;
  
      &:hover,
      &.is-active {
        opacity: 1;
      }
    }
  }
  