const mongoose = require('mongoose');
const File = require('./api/models/file');

// Connect to MongoDB
mongoose.connect('mongodb://localhost:27017/blog', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

// Function to determine correct file type based on MIME type
function getCorrectFileType(mimetype) {
  if (mimetype.startsWith('image/')) {
    return 'image';
  } else if (mimetype.startsWith('video/')) {
    return 'video';
  } else if (
    mimetype === 'application/pdf' ||
    mimetype === 'application/msword' ||
    mimetype === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||
    mimetype === 'application/vnd.ms-excel' ||
    mimetype === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
    mimetype === 'application/vnd.openxmlformats-officedocument.presentationml.presentation' ||
    mimetype === 'text/plain' ||
    mimetype === 'text/csv' ||
    mimetype === 'application/x-rar-compressed' ||
    mimetype === 'application/vnd.rar' ||
    mimetype === 'application/zip' ||
    mimetype === 'application/x-zip-compressed'
  ) {
    return 'document';
  } else {
    return 'other';
  }
}

async function fixFileTypes() {
  try {
    console.log('Starting file type correction...');
    
    // Find all files
    const files = await File.find({});
    console.log(`Found ${files.length} files to check`);
    
    let correctedCount = 0;
    
    for (const file of files) {
      const correctType = getCorrectFileType(file.mimetype);
      
      if (file.type !== correctType) {
        console.log(`Correcting file: ${file.originalName}`);
        console.log(`  MIME type: ${file.mimetype}`);
        console.log(`  Current type: ${file.type} -> Correct type: ${correctType}`);
        
        file.type = correctType;
        await file.save();
        correctedCount++;
      }
    }
    
    console.log(`\nFile type correction completed!`);
    console.log(`Total files checked: ${files.length}`);
    console.log(`Files corrected: ${correctedCount}`);
    
    // Show summary by type
    const summary = await File.aggregate([
      {
        $group: {
          _id: '$type',
          count: { $sum: 1 }
        }
      }
    ]);
    
    console.log('\nFile type summary:');
    summary.forEach(item => {
      console.log(`  ${item._id}: ${item.count} files`);
    });
    
  } catch (error) {
    console.error('Error fixing file types:', error);
  } finally {
    mongoose.connection.close();
  }
}

// Run the fix
fixFileTypes();
