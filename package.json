{"name": "blog-express", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@fingerprintjs/fingerprintjs": "^4.6.0", "@hookform/resolvers": "^3.10.0", "@minoru/react-dnd-treeview": "^3.5.0", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@react-oauth/google": "^0.12.1", "@tanstack/react-query": "^5.64.2", "@tanstack/react-table": "^8.20.6", "@tiptap/extension-bubble-menu": "^2.11.3", "@tiptap/extension-character-count": "^2.11.3", "@tiptap/extension-color": "^2.11.7", "@tiptap/extension-font-family": "^2.11.7", "@tiptap/extension-highlight": "^2.11.3", "@tiptap/extension-image": "^2.11.3", "@tiptap/extension-link": "^2.11.3", "@tiptap/extension-table": "^2.11.7", "@tiptap/extension-table-cell": "^2.11.7", "@tiptap/extension-table-header": "^2.11.7", "@tiptap/extension-table-row": "^2.11.7", "@tiptap/extension-task-item": "^2.11.7", "@tiptap/extension-task-list": "^2.11.7", "@tiptap/extension-text-align": "^2.11.3", "@tiptap/extension-text-style": "^2.11.7", "@tiptap/pm": "^2.11.3", "@tiptap/react": "^2.11.3", "@tiptap/starter-kit": "^2.11.3", "algoliasearch": "^4.24.0", "axios": "^1.7.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cookies-next": "^4.3.0", "crypto-js": "^4.2.0", "daisyui": "^4.12.10", "dnd-kit-sortable-tree": "^0.1.73", "dotenv": "^16.4.5", "express": "^4.19.2", "flowbite": "^2.4.1", "flowbite-react": "^0.10.1", "idb": "^8.0.0", "instantsearch.css": "^8.3.0", "js-cookie": "^3.0.5", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.408.0", "nanoid": "^5.0.9", "next": "^14.2.5", "next-pwa": "^5.6.0", "next-share": "^0.27.0", "next-sitemap": "^4.2.3", "nextjs-toploader": "^1.6.12", "npm": "^10.8.2", "papaparse": "^5.5.2", "pdfjs-dist": "^2.16.105", "plyr": "^3.7.8", "plyr-react": "^5.3.0", "prop-types": "^15.8.1", "react": "^18.3.1", "react-colorful": "^5.6.1", "react-complex-tree": "^2.4.6", "react-dnd": "^16.0.1", "react-dom": "^18.3.1", "react-feather": "^2.0.10", "react-google-recaptcha": "^3.1.0", "react-hook-form": "^7.52.1", "react-instantsearch": "^7.12.2", "react-pdf": "^9.2.1", "react-query": "^3.39.3", "react-slick": "^0.30.3", "react-toastify": "^10.0.5", "sass": "^1.83.4", "screenfull": "^6.0.2", "sharp": "^0.33.4", "slick-carousel": "^1.8.1", "sortablejs": "^1.15.6", "swr": "^2.2.5", "tippy.js": "^6.3.7", "xlsx": "^0.18.5", "zod": "^3.24.1"}, "devDependencies": {"@types/js-cookie": "^3.0.6", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/sortablejs": "^1.15.8", "eslint": "^8", "eslint-config-next": "14.2.5", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}