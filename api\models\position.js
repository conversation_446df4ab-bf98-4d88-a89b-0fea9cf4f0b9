const mongoose = require("mongoose");
const { Schema } = mongoose;

const positionSchema = new Schema(
  {
    name: {
      type: String,
      required: true,
      trim: true
    },
    description: {
      type: String,
      default: "",
      trim: true
    },
    department: {
      type: Schema.Types.ObjectId,
      ref: "Department",
      required: true
    },
    level: {
      type: Number,
      default: 1, // 1 = thấp nhất, số càng cao quyền càng lớn
      min: 1,
      max: 10
    },
    permissions: [{
      type: String,
      enum: ["create", "read", "update", "delete", "manage_users", "manage_categories", "manage_posts", "view_analytics"]
    }],
    isActive: {
      type: Boolean,
      default: true
    },
    isDefault: {
      type: Boolean,
      default: false // Đánh dấu position mặc định không thể xóa
    },
    order: {
      type: Number,
      default: 0 // Thứ tự hiển thị trong phòng ban
    },
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: "User"
    }
  },
  { timestamps: true }
);

// Compound index to ensure unique position names within each department
positionSchema.index({ name: 1, department: 1 }, { unique: true });

// Index for performance
positionSchema.index({ department: 1, isActive: 1, order: 1 });
positionSchema.index({ isActive: 1 });

// Static method để tạo positions mặc định cho một department (DISABLED)
positionSchema.statics.createDefaultPositionsForDepartment = async function(departmentId, createdBy = null) {
  // Default positions creation is now disabled
  console.log(`Default positions creation is disabled for department ${departmentId}`);
  return;
};

// Instance method to check if position can be deleted
positionSchema.methods.canBeDeleted = function() {
  return !this.isDefault;
};

module.exports = mongoose.model("Position", positionSchema);