const express = require("express");
const router = express.Router();
const passport = require("passport");
const {
  getAllDepartments,
  createDepartment,
  updateDepartment,
  updateDepartmentLevel,
  deleteDepartment,
  reorderDepartments,
  normalizeDepartmentLevels
} = require("../controllers/department");
const { verifyAdmin } = require("../middleware/is-admin");

// Admin routes for departments
router.get("/", passport.authenticate('user', { session: false }), verifyAdmin, getAllDepartments);
router.post("/", passport.authenticate('user', { session: false }), verifyAdmin, createDepartment);
router.put("/normalize", passport.authenticate('user', { session: false }), verifyAdmin, async (req, res) => {
  try {
    const normalizedDepartments = await normalizeDepartmentLevels();
    res.status(200).json({
      success: true,
      data: normalizedDepartments,
      message: "Chuẩn hóa level phòng ban thành công"
    });
  } catch (error) {
    console.error("Error normalizing department levels:", error);
    res.status(500).json({
      success: false,
      message: "Lỗi server khi chuẩn hóa level phòng ban"
    });
  }
});
router.put("/reorder", passport.authenticate('user', { session: false }), verifyAdmin, reorderDepartments);
router.put("/:id/level", passport.authenticate('user', { session: false }), verifyAdmin, updateDepartmentLevel);
router.put("/:id", passport.authenticate('user', { session: false }), verifyAdmin, updateDepartment);
router.delete("/:id", passport.authenticate('user', { session: false }), verifyAdmin, deleteDepartment);

module.exports = router;
