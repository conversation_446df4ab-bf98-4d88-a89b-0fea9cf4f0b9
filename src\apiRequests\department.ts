import http from "@/lib/http";

export interface Department {
  _id: string;
  name: string;
  level?: number;
}

export interface DepartmentListResType {
  success: boolean;
  data: Department[];
  message: string;
}

export interface DepartmentResType {
  success: boolean;
  data: Department;
  message: string;
}

export interface MessageResType {
  success: boolean;
  message: string;
}

const departmentApiRequest = {
  // Get all departments (admin only)
  getDepartments: (sessionToken: string) =>
    http.get<DepartmentListResType>("/api/departments", {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),

  // Create new department (admin only)
  createDepartment: (sessionToken: string, name: string) =>
    http.post<DepartmentResType>("/api/departments", { name }, {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),

  // Update department (admin only)
  updateDepartment: (sessionToken: string, id: string, name: string, level?: number) =>
    http.put<DepartmentResType>(`/api/departments/${id}`, { name, level }, {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),

  // Delete department (admin only)
  deleteDepartment: (sessionToken: string, id: string) =>
    http.delete<MessageResType>(`/api/departments/${id}`, {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),

  // Update department level with automatic shifting (admin only)
  updateDepartmentLevel: (sessionToken: string, id: string, level: number) =>
    http.put<DepartmentListResType>(`/api/departments/${id}/level`, { level }, {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),

  // Reorder departments (admin only)
  reorderDepartments: (sessionToken: string, departmentOrder: Array<{_id: string, level: number}>) =>
    http.put<DepartmentListResType>("/api/departments/reorder", { departmentOrder }, {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),

  // Normalize department levels (admin only)
  normalizeDepartmentLevels: (sessionToken: string) =>
    http.put<DepartmentListResType>("/api/departments/normalize", {}, {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),
};

export default departmentApiRequest;
