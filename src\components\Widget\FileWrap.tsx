"use client";
import { useEffect, useState } from "react";
import dynamic from "next/dynamic";
import envConfig from "@/config";

// Load ExcelViewer dynamically (client-side only)
const ExcelViewer = dynamic(() => import("@/components/Widget/ExcelViewer"), { ssr: false });

const FileWrap = ({ blog }) => {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    // ✅ Detect mobile safely
    const userAgent = navigator.userAgent || navigator.vendor;
    setIsMobile(/iPhone|iPad|iPod|Android/i.test(userAgent));
  }, []);

  // Debug logging
  console.log("FileWrap received blog:", blog);
  console.log("FileWrap blog.file:", blog?.file);
  console.log("FileWrap blog.file length:", blog?.file?.length);

  // Debug each file item
  if (blog?.file) {
    blog.file.forEach((fileItem, index) => {
      console.log(`File ${index}:`, fileItem);
      console.log(`File ${index} path:`, fileItem?.path);
      console.log(`File ${index} type:`, fileItem?.type);
    });
  }

  if (!blog?.file || blog.file.length === 0) {
    console.log("FileWrap: No files to display, returning null");
    return null; // ✅ Check for empty array
  }

  const allowedIframeTypes = ["pdf"];

  return (
    <div className="file-wrap relative space-y-4">
      {blog.file.map((fileItem, index) => {
        // Check if fileItem and fileItem.path exist
        if (!fileItem || !fileItem.path) {
          console.warn(`Invalid file item at index ${index}:`, fileItem);
          return null;
        }

        // Construct proper file URL
        const fileUrl = fileItem.path.startsWith('http')
          ? fileItem.path
          : `${envConfig.NEXT_PUBLIC_API_ENDPOINT}${fileItem.path}`;

        const fileName = fileItem.originalName || fileItem.path.split('/').pop() || 'Unknown File';
        const fileExt = (fileItem.type?.split('/')[1] || fileItem.path.split(".").pop())?.toLowerCase() || "";

        console.log(`Processing file ${index}:`, {
          fileUrl,
          fileName,
          fileExt,
          originalType: fileItem.type
        });

        const iframeSrc = isMobile
          ? `https://docs.google.com/gview?embedded=true&url=${encodeURIComponent(fileUrl)}`
          : fileUrl;

        return (
          <div key={index} className="max-w-[680px] w-full">
            {fileExt === "xlsx" ? (
              <ExcelViewer fileUrl={fileUrl} />
            ) : allowedIframeTypes.includes(fileExt) ? (
              <div className="relative">
                <iframe
                  src={iframeSrc}
                  className="w-full border-none h-screen"
                  style={{ overflow: "auto" }}
                  onError={(e) => {
                    console.error("Iframe loading error:", e);
                  }}
                  title={`PDF Viewer - ${fileName}`}
                ></iframe>
                <div className="absolute top-2 right-2 bg-black bg-opacity-50 text-white p-2 rounded text-xs">
                  {fileName}
                </div>
              </div>
            ) : (
              <div className="p-4 border rounded bg-gray-50">
                <p className="text-gray-600">File type not supported for preview: {fileExt}</p>
                <p className="text-sm text-gray-500">Use the download button below to access the file.</p>
              </div>
            )}
            <div className="flex items-center justify-between mt-4 border p-4 rounded bg-white">
              <div>
                <span className="text-sm text-gray-700">File Đính kèm: </span>
                <span className="text-sm font-medium">{fileName}</span>
                {fileItem.size && (
                  <span className="text-xs text-gray-500 ml-2">
                    ({Math.round(fileItem.size / 1024)} KB)
                  </span>
                )}
              </div>
              <a
                href={fileUrl}
                download={fileName}
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                Download File
              </a>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default FileWrap;
