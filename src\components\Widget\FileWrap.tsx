"use client";
import { useEffect, useState } from "react";
import dynamic from "next/dynamic";
import envConfig from "@/config";

// Load ExcelViewer dynamically (client-side only)
const ExcelViewer = dynamic(() => import("@/components/Widget/ExcelViewer"), { ssr: false });

const FileWrap = ({ blog }) => {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    // ✅ Detect mobile safely
    const userAgent = navigator.userAgent || navigator.vendor;
    setIsMobile(/iPhone|iPad|iPod|Android/i.test(userAgent));
  }, []);

  if (!blog?.file || blog.file.length === 0) return null; // ✅ Check for empty array

  const allowedIframeTypes = ["pdf"];

  return (
    <div className="file-wrap relative space-y-4">
      {blog.file.map((fileItem, index) => {
        // Check if fileItem and fileItem.path exist
        if (!fileItem || typeof fileItem !== 'object') {
          console.warn(`Invalid file item at index ${index}:`, fileItem);
          return null;
        }

        // Handle different possible file object structures
        let filePath = '';
        if (fileItem.path) {
          filePath = fileItem.path;
        } else if (typeof fileItem === 'string') {
          filePath = fileItem;
        } else {
          console.warn(`File item missing path at index ${index}:`, fileItem);
          return null;
        }

        const fileUrl = `${envConfig.NEXT_PUBLIC_API_ENDPOINT}${filePath}`;
        const fileName = filePath.replace("/uploads/file/", "");
        const fileExt = filePath.split(".").pop()?.toLowerCase() || "";

        const iframeSrc = isMobile
          ? `https://docs.google.com/gview?embedded=true&url=${encodeURIComponent(fileUrl)}`
          : fileUrl;

        return (
          <div key={index} className=" max-w-[680px] w-full">
            {fileExt === "xlsx" ? (
              <ExcelViewer fileUrl={fileUrl} />
            ) : allowedIframeTypes.includes(fileExt) ? (
              <iframe
                src={iframeSrc}
                className="w-full border-none h-screen"
                style={{ overflow: "auto" }}
              ></iframe>
            ) : (
              ""
            )}
            <div className="flex items-center justify-between mt-4 border p-4 rounded bg-white">
                <span className="text-sm text-gray-700">File Đính kèm</span>
                <a
                  href={fileUrl}
                  download
                  className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                >
                  Download File
                </a>
              </div>
          </div>
        );
      })}
    </div>
  );
};

export default FileWrap;
